#include <stdio.h>
#include <stdlib.h>

#define tamanho 9

char tabuleiro[tamanho][tamanho];
int jogadorAtual = 1;

void telaMenu();
void mostrarTabuleiro(char tabuleiro[tamanho][tamanho]);
void escolhaJogada();
void inicializarTabuleiro();
int validarMovimento(int lin1, int col1, int lin2, int col2);
void moverPeca(int lin1, int col1, int lin2, int col2);
int temCaptura();
int podeCapturar(int lin, int col);

int main()
{
    telaMenu();
    return 0;
}

void telaMenu(){

    int opcaoMenu;
    printf("\n|====================================================|");
    printf("\n|                    JOGO DAMAS                      |");
    printf("\n|====================================================|");
    printf("\n|                                                    |");
    printf("\n|                       Menu                         |");
    printf("\n|             [1] Jogador X Jogador                  |");
    printf("\n|             [2] Jogador X Computador               |");
    printf("\n|             [3] Ajuda                              |");
    printf("\n|             [4] Autores                            |");
    printf("\n|             [5] Sair                               |");
    printf("\n|                                                    |");
    printf("\n|====================================================|");
    printf("\n");
    printf("\n Escolha uma opcao: ");
    scanf("%d", &opcaoMenu);

    switch(opcaoMenu){

        case 1:
            system("cls");
            inicializarTabuleiro();
            mostrarTabuleiro(tabuleiro);
            escolhaJogada();
            break;

        case 2:
            system("cls");
            telaMenu();
            break;

        case 3:
            system("cls");
            printf("\n\n|                                           INFORMACOES                                                |");
            printf("\n|   - Tabuleiro: tabuleiro 8x8 possuindo 12 pecas de cada lado.                                        |");
            printf("\n|   - Movimento: As pecas movem-se diagonalmente somente para a frente.                                |");
            printf("\n|   - Movimentos de Captura: As pecas podem capturar adversarios pulando sobre eles diagonalmente,     |");
            printf("\n|  se possui uma opcao de capturar uma peca eh obrigatorio capturar-la,                                |");
            printf("\n|  eh somente permitido capturar uma peca que nao possua nenhuma outra peca atras.                     |");
            printf("\n|   - Promocao a Dama: Quando um pecas chega ao extremo oposto do tabuleiro, ele se torna um dama,     |");
            printf("\n|  permitindo andar livremente pelas diagonais.                                                        |");
            printf("\n|   - Empate: Apos 20 turnos sem captura ocorrera o empate                                             |");
            printf("\n\n Pressione ENTER para continuar...");
            while (getchar() != '\n');
            getchar();
            system("cls");
            telaMenu();
            break;

        case 4:
            system("cls");
            printf("\n\n|                  AUTORES:                         |");
            printf("\n|                                                   |");
            printf("\n|      Tiago Citrangulo da Silva                    |");
            printf("\n|      Pedro Henrique Baraldi                       |");
            printf("\n|      Luana Gallinari R da Silva                   |");
            printf("\n|      Victor Wesley Oliveira dos Santos            |");
            printf("\n|      Jean Silva                                   |");
            printf("\n|      Gustavo Ferreira dos Santos                  |");
            printf("\n\n Pressione ENTER para continuar...");
            while (getchar() != '\n');
            getchar();
            system("cls");
            telaMenu();
            break;

        case 5:
            system("cls");
            printf("\n\n--------- Jogo encerrado! ---------\n");
            break;

        default:
            printf("\n Opcao invalida, escolha entre uma das opcoes dadas.");
            printf("\n Pressione ENTER para continuar...");
            while (getchar() != '\n');
            getchar();
            system("cls");
            telaMenu();
            break;
    }
}

void inicializarTabuleiro(){
    for (int i = 0; i < tamanho; i++) {
        for (int j = 0; j < tamanho; j++) {
            tabuleiro[i][j] = ' ';
        }
    }

    for (int i = 1; i <= 3; i++) {
        for (int j = 1; j <= 8; j++) {
            if ((i + j) % 2 == 0) {
                tabuleiro[i][j] = 'X';
            }
        }
    }

    for (int i = 6; i <= 8; i++) {
        for (int j = 1; j <= 8; j++) {
            if ((i + j) % 2 == 0) {
                tabuleiro[i][j] = 'O';
            }
        }
    }
}

void escolhaJogada(){

    int opcaoTabuleiro;
    printf("|                 Vez do Jogador %d                        |\n", jogadorAtual);
    printf("|                                                      |");
    printf("\n|       [6] Fazer jogada            [7] Sair           |");
    printf("\n|                                                      |\n");
    printf("\n Escolha uma opcao: ");
    scanf("%d", &opcaoTabuleiro);

    switch(opcaoTabuleiro){

        case 6: {
            int lin1, col1, lin2, col2;
            printf("\n - Digite o numero da linha inicial (1-8): ");
            scanf("%d", &lin1);
            printf(" - Digite o numero da coluna inicial (1-8): ");
            scanf("%d", &col1);
            printf(" - Digite o numero da linha final (1-8): ");
            scanf("%d", &lin2);
            printf(" - Digite o numero da coluna final (1-8): ");
            scanf("%d", &col2);

            if (validarMovimento(lin1, col1, lin2, col2)) {
                moverPeca(lin1, col1, lin2, col2);
                jogadorAtual = (jogadorAtual == 1) ? 2 : 1;
                system("cls");
                mostrarTabuleiro(tabuleiro);
            } else {
                system("cls");
                mostrarTabuleiro(tabuleiro);
            }
            escolhaJogada();
            break;
        }

        case 7:
            system("cls");
            telaMenu();
            break;

        default:
            printf("\n Opcao invalida, escolha entre uma das opcoes dadas.");
            printf("\n Pressione ENTER para continuar...");
            while (getchar() != '\n');
            getchar();
            system("cls");
            mostrarTabuleiro(tabuleiro);
            escolhaJogada();
            break;
    }
}

int validarMovimento(int lin1, int col1, int lin2, int col2) {
    if (lin1 < 1) {
        return 0;
    }
    if (lin1 > 8) {
        return 0;
    }
    if (col1 < 1) {
        return 0;
    }
    if (col1 > 8) {
        return 0;
    }
    if (lin2 < 1) {
        return 0;
    }
    if (lin2 > 8) {
        return 0;
    }
    if (col2 < 1) {
        return 0;
    }
    if (col2 > 8) {
        return 0;
    }

    if (tabuleiro[lin1][col1] == ' ') {
        return 0;
    }

    char peca;
    if (jogadorAtual == 1) {
        peca = 'X';
    } else {
        peca = 'O';
    }

    if (tabuleiro[lin1][col1] != peca) {
        return 0;
    }

    if (tabuleiro[lin2][col2] != ' ') {
        return 0;
    }

    int dif1, dif2;
    if (lin2 > lin1) {
        dif1 = lin2 - lin1;
    } else {
        dif1 = lin1 - lin2;
    }
    if (col2 > col1) {
        dif2 = col2 - col1;
    } else {
        dif2 = col1 - col2;
    }

    if (dif1 != dif2) {
        return 0;
    }

    if (temCaptura() == 1) {
        if (dif1 != 2) {
            return 0;
        }
        int linMeio = (lin1 + lin2) / 2;
        int colMeio = (col1 + col2) / 2;
        char inimigo;
        if (peca == 'X') {
            inimigo = 'O';
        } else {
            inimigo = 'X';
        }
        if (tabuleiro[linMeio][colMeio] != inimigo) {
            return 0;
        }
    } else {
        if (dif1 != 1) {
            return 0;
        }
    }

    if (peca == 'X') {
        if (lin2 <= lin1) {
            return 0;
        }
    }
    if (peca == 'O') {
        if (lin2 >= lin1) {
            return 0;
        }
    }

    return 1;
}

void moverPeca(int lin1, int col1, int lin2, int col2) {
    char temp;
    temp = tabuleiro[lin1][col1];
    tabuleiro[lin1][col1] = ' ';
    tabuleiro[lin2][col2] = temp;

    int dif1, dif2;
    if (lin2 > lin1) {
        dif1 = lin2 - lin1;
    } else {
        dif1 = lin1 - lin2;
    }
    if (col2 > col1) {
        dif2 = col2 - col1;
    } else {
        dif2 = col1 - col2;
    }

    if (dif1 == 2) {
        int linMeio = (lin1 + lin2) / 2;
        int colMeio = (col1 + col2) / 2;
        tabuleiro[linMeio][colMeio] = ' ';
    }
}

void mostrarTabuleiro(char tabuleiro[tamanho][tamanho]){

    printf("\n");
    printf("\n\n|======================================================|");
    printf("\n|                  TABULEIRO DAMAS                     |");
    printf("\n|======================================================|");
    printf("\n|      1     2     3     4     5     6     7     8     |\n");

    for (int i = 1; i < tamanho; i++) {
        printf("|  %d ", i);
        for (int j = 1; j < tamanho; j++) {
            printf("[ %c ] ", tabuleiro[i][j]);
        }
        printf("|\n");
    }
    printf("|======================================================|\n");
    printf("\n Legenda: X = Jogador 1, O = Jogador 2\n");
}

int temCaptura() {
    char peca;
    if (jogadorAtual == 1) {
        peca = 'X';
    } else {
        peca = 'O';
    }

    for (int i = 1; i <= 8; i++) {
        for (int j = 1; j <= 8; j++) {
            if (tabuleiro[i][j] == peca) {
                if (podeCapturar(i, j) == 1) {
                    return 1;
                }
            }
        }
    }
    return 0;
}

int podeCapturar(int lin, int col) {
    char peca = tabuleiro[lin][col];
    char inimigo;
    if (peca == 'X') {
        inimigo = 'O';
    } else {
        inimigo = 'X';
    }

    int direcoes[4][2] = {{-1, -1}, {-1, 1}, {1, -1}, {1, 1}};

    for (int d = 0; d < 4; d++) {
        int linMeio = lin + direcoes[d][0];
        int colMeio = col + direcoes[d][1];
        int linFinal = lin + 2 * direcoes[d][0];
        int colFinal = col + 2 * direcoes[d][1];

        if (linMeio >= 1 && linMeio <= 8 && colMeio >= 1 && colMeio <= 8) {
            if (linFinal >= 1 && linFinal <= 8 && colFinal >= 1 && colFinal <= 8) {
                if (tabuleiro[linMeio][colMeio] == inimigo) {
                    if (tabuleiro[linFinal][colFinal] == ' ') {
                        if (peca == 'X' && linFinal > lin) {
                            return 1;
                        }
                        if (peca == 'O' && linFinal < lin) {
                            return 1;
                        }
                    }
                }
            }
        }
    }
    return 0;
}